"""
Main shipping service that orchestrates between Rapidshyp and fallback methods
"""

import logging
from typing import Dict, List, Optional, Union
from decimal import Decimal
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.db import transaction

from orders.models import Order, ShippingMethod
from ..models import RapidshypShipment, RapidshypConfiguration, TrackingEvent
from .rapidshyp_client import RapidshypClient
from .fallback_service import FallbackShippingService
from ..exceptions import (
    ShippingException,
    RapidshypAPIException,
    OrderProcessingException
)
from ..utils import (
    calculate_package_dimensions,
    get_minimum_rate,
    format_currency
)
from ..constants import RAPIDSHYP_TO_ORDER_STATUS


logger = logging.getLogger(__name__)


class ShippingService:
    """
    Main shipping service that provides unified interface for shipping operations
    """

    def __init__(self):
        self.rapidshyp_client = RapidshypClient()
        self.fallback_service = FallbackShippingService()
        self.rapidshyp_enabled = getattr(settings, 'RAPIDSHYP_ENABLED', False)
        self.logger = logger

    def get_shipping_rates(self, pickup_pincode: str, delivery_pincode: str,
                          weight: float = 1.0, cod: bool = False,
                          total_value: float = 0) -> Dict:
        """
        Get shipping rates from Rapidshyp with fallback to existing methods

        Args:
            pickup_pincode: Pickup pincode
            delivery_pincode: Delivery pincode
            weight: Package weight in kg
            cod: Cash on delivery flag
            total_value: Total order value

        Returns:
            Dict: Shipping rates response with both Rapidshyp and existing methods
        """
        response = {
            'success': True,
            'rapidshyp_rates': [],
            'existing_methods': [],
            'source': 'fallback',
            'message': '',
            'minimum_rate': None,
            'pickup_pincode': pickup_pincode,
            'delivery_pincode': delivery_pincode,
            'weight': weight,
            'cod': cod,
            'total_value': total_value
        }

        # Always get existing methods as fallback
        try:
            existing_methods = self.fallback_service.get_existing_methods()
            response['existing_methods'] = existing_methods
            self.logger.debug(f"Retrieved {len(existing_methods)} existing shipping methods")
        except Exception as e:
            self.logger.error(f"Failed to get existing methods: {e}")
            existing_methods = []

        # Try Rapidshyp if enabled and configured
        if self.rapidshyp_enabled and self.rapidshyp_client.api_key:
            try:
                rapidshyp_response = self.rapidshyp_client.check_serviceability(
                    pickup_pincode=pickup_pincode,
                    delivery_pincode=delivery_pincode,
                    cod=cod,
                    total_order_value=total_value,
                    weight=weight
                )

                if rapidshyp_response.get('status') and rapidshyp_response.get('serviceable_courier_list'):
                    raw_rapidshyp_rates = rapidshyp_response['serviceable_courier_list']
                    # Process the raw rates to add estimated_days from EDD
                    rapidshyp_rates = self._process_rapidshyp_rates(raw_rapidshyp_rates)
                    response['rapidshyp_rates'] = rapidshyp_rates
                    response['source'] = 'rapidshyp'
                    response['message'] = 'Live shipping rates from Rapidshyp'

                    self.logger.info(f"Rapidshyp rates retrieved: {len(rapidshyp_rates)} couriers available")
                else:
                    self.logger.warning("Rapidshyp returned no serviceable couriers")
                    response['message'] = 'Using standard shipping rates'

            except Exception as e:
                self.logger.warning(f"Rapidshyp rate calculation failed, using fallback: {e}")
                response['message'] = 'Using standard shipping rates (Rapidshyp unavailable)'
        else:
            response['message'] = 'Using standard shipping rates'

        # Combine all rates and find minimum
        all_rates = response['rapidshyp_rates'] + response['existing_methods']
        if all_rates:
            response['minimum_rate'] = get_minimum_rate(all_rates)
            self.logger.debug(f"Minimum rate: {format_currency(response['minimum_rate']['total_freight'])}")

        return response

    def _process_rapidshyp_rates(self, raw_rates: List[Dict]) -> List[Dict]:
        """
        Process raw Rapidshyp rates to add calculated estimated_days from EDD

        Args:
            raw_rates: Raw rates from Rapidshyp API

        Returns:
            List[Dict]: Processed rates with estimated_days calculated from EDD
        """
        processed_rates = []

        for rate in raw_rates:
            try:
                # Create a copy of the rate
                processed_rate = rate.copy()

                # Calculate estimated_days from EDD (Estimated Delivery Date)
                edd = rate.get('edd')
                estimated_days = self._calculate_delivery_days_from_edd(edd)

                # Add the calculated estimated_days
                processed_rate['estimated_days'] = estimated_days

                # Keep the original EDD and EPD for reference
                processed_rate['estimated_delivery_date'] = edd
                processed_rate['estimated_pickup_date'] = rate.get('epd')

                processed_rates.append(processed_rate)

            except Exception as e:
                self.logger.warning(f"Error processing Rapidshyp rate: {e}")
                # Include the rate anyway with default estimated_days
                rate_copy = rate.copy()
                rate_copy['estimated_days'] = 5  # Default fallback
                processed_rates.append(rate_copy)

        return processed_rates

    def _calculate_delivery_days_from_edd(self, edd_string: str) -> int:
        """
        Calculate estimated delivery days from EDD string

        Args:
            edd_string: EDD in format "DD-MM-YYYY"

        Returns:
            int: Number of days from today to delivery
        """
        if not edd_string:
            return 5  # Default fallback

        try:
            from datetime import datetime, date

            # Parse EDD string (format: "24-06-2025")
            edd_date = datetime.strptime(edd_string, "%d-%m-%Y").date()
            today = date.today()

            # Calculate difference in days
            days_diff = (edd_date - today).days

            # Ensure reasonable bounds (1-30 days)
            if days_diff < 1:
                return 1
            elif days_diff > 30:
                return 30
            else:
                return days_diff

        except (ValueError, TypeError) as e:
            self.logger.warning(f"Error parsing EDD '{edd_string}': {e}")
            return 5  # Default fallback

    def create_rapidshyp_order(self, order: Order, courier_code: str,
                              delivery_pincode: str, package_weight: float) -> Dict:
        """
        Create Rapidshyp order for Django order

        Args:
            order: Django Order instance
            courier_code: Selected courier code
            delivery_pincode: Delivery pincode
            package_weight: Package weight

        Returns:
            Dict: Order creation result
        """
        if not self.rapidshyp_enabled:
            raise OrderProcessingException("Rapidshyp is not enabled")

        try:
            # Get Rapidshyp configuration
            config = RapidshypConfiguration.objects.filter(is_active=True).first()
            if not config:
                raise OrderProcessingException("Rapidshyp configuration not found")

            # Calculate package dimensions
            package_dimensions = calculate_package_dimensions(order.items.all())

            # Prepare order data for Rapidshyp
            order_data = self._prepare_rapidshyp_order_data(
                order=order,
                config=config,
                courier_code=courier_code,
                delivery_pincode=delivery_pincode,
                package_weight=package_weight,
                package_dimensions=package_dimensions
            )

            # Create order via Rapidshyp wrapper API
            rapidshyp_response = self.rapidshyp_client.create_order(order_data)

            if rapidshyp_response.get('status'):
                # Create RapidshypShipment record
                shipment = self._create_rapidshyp_shipment(order, rapidshyp_response, courier_code)

                self.logger.info(f"Rapidshyp order created successfully: {shipment.rapidshyp_order_id}")

                return {
                    'success': True,
                    'rapidshyp_order_id': shipment.rapidshyp_order_id,
                    'shipment_id': shipment.id,
                    'message': 'Rapidshyp order created successfully'
                }
            else:
                error_msg = rapidshyp_response.get('message', 'Order creation failed')
                raise OrderProcessingException(error_msg)

        except Exception as e:
            self.logger.error(f"Rapidshyp order creation failed: {e}")
            raise OrderProcessingException(f"Rapidshyp order creation failed: {e}")

    def sync_tracking_status(self, shipment: RapidshypShipment) -> bool:
        """
        Sync tracking status for Rapidshyp shipment

        Args:
            shipment: RapidshypShipment instance

        Returns:
            bool: True if sync was successful
        """
        try:
            # Get tracking data from Rapidshyp
            tracking_data = self.rapidshyp_client.track_shipment(
                awb=shipment.awb_number,
                order_id=shipment.rapidshyp_order_id
            )

            if tracking_data.get('status'):
                # Update shipment status
                old_status = shipment.current_status
                new_status = tracking_data.get('current_status', old_status)

                if new_status != old_status:
                    shipment.current_status = new_status
                    shipment.status_description = tracking_data.get('status_description', '')
                    shipment.save()

                    # Create tracking event
                    TrackingEvent.objects.create(
                        shipment=shipment,
                        status=new_status,
                        status_description=tracking_data.get('status_description', ''),
                        location=tracking_data.get('location', ''),
                        event_timestamp=timezone.now()
                    )

                    # Update order status if needed
                    self._update_order_status_from_shipment(shipment)

                    self.logger.info(f"Tracking status updated: {shipment.rapidshyp_order_id} -> {new_status}")

                return True
            else:
                self.logger.warning(f"Tracking sync failed for {shipment.rapidshyp_order_id}")
                return False

        except Exception as e:
            self.logger.error(f"Tracking sync failed: {e}")
            return False

    def get_tracking_info(self, shipment: RapidshypShipment) -> Dict:
        """
        Get comprehensive tracking information for shipment

        Args:
            shipment: RapidshypShipment instance

        Returns:
            Dict: Tracking information
        """
        try:
            # Get latest tracking data
            tracking_data = self.rapidshyp_client.track_shipment(
                awb=shipment.awb_number,
                order_id=shipment.rapidshyp_order_id
            )

            # Get tracking events from database
            tracking_events = shipment.tracking_events.all()[:10]  # Last 10 events

            return {
                'success': True,
                'shipment_id': str(shipment.id),
                'rapidshyp_order_id': shipment.rapidshyp_order_id,
                'awb_number': shipment.awb_number,
                'courier_name': shipment.courier_name,
                'current_status': shipment.current_status,
                'status_description': shipment.status_description,
                'tracking_url': shipment.tracking_url,
                'expected_delivery_date': shipment.expected_delivery_date.isoformat() if shipment.expected_delivery_date else None,
                'actual_delivery_date': shipment.actual_delivery_date.isoformat() if shipment.actual_delivery_date else None,
                'tracking_events': [
                    {
                        'status': event.status,
                        'description': event.status_description,
                        'location': event.location,
                        'timestamp': event.event_timestamp.isoformat(),
                        'remarks': event.remarks
                    }
                    for event in tracking_events
                ],
                'live_tracking': tracking_data if tracking_data.get('status') else None
            }

        except Exception as e:
            self.logger.error(f"Failed to get tracking info: {e}")
            return {
                'success': False,
                'error': str(e),
                'shipment_id': str(shipment.id),
                'rapidshyp_order_id': shipment.rapidshyp_order_id
            }

    def _prepare_rapidshyp_order_data(self, order: Order, config: RapidshypConfiguration,
                                    courier_code: str, delivery_pincode: str,
                                    package_weight: float, package_dimensions: Dict) -> Dict:
        """
        Prepare order data for Rapidshyp wrapper API

        Args:
            order: Django Order instance
            config: RapidshypConfiguration instance
            courier_code: Selected courier code
            delivery_pincode: Delivery pincode
            package_weight: Package weight
            package_dimensions: Package dimensions

        Returns:
            Dict: Formatted order data for Rapidshyp wrapper API
        """
        # Format pickup location
        pickup_location = {
            'contactName': config.contact_name,
            'pickupName': config.pickup_address_name,
            'pickupEmail': config.contact_email,
            'pickupPhone': config.contact_phone,
            'pickupAddress1': config.address_line_1,
            'pickupAddress2': config.address_line_2,
            'pinCode': config.default_pickup_pincode
        }

        # Format shipping address
        # Split customer name into first and last name
        customer_name = order.user.name or ''
        name_parts = customer_name.split(' ', 1)
        first_name = name_parts[0] if name_parts else ''
        last_name = name_parts[1] if len(name_parts) > 1 else ''

        shipping_address = {
            'firstName': first_name,
            'lastName': last_name,
            'addressLine1': order.shipping_address.street_address,
            'addressLine2': order.shipping_address.apartment or '',
            'pinCode': delivery_pincode,
            'email': order.shipping_address.order_user_email or order.user.email,
            'phone': order.shipping_address.order_user_phone or order.user.phone_number
        }

        # Format order items
        order_items = []
        for item in order.items.all():
            order_items.append({
                'itemName': item.product_name,
                'sku': getattr(item.product, 'sku', f'SKU-{item.product.id}') if item.product else f'ITEM-{item.id}',
                'description': getattr(item.product, 'description', item.product_name)[:100] if item.product else item.product_name,
                'units': item.quantity,
                'unitPrice': float(item.unit_price),
                'tax': 0.0,  # Tax is handled separately in our system
                'hsn': '',  # HSN code if available
                'productLength': 10.0,  # Default dimensions
                'productBreadth': 10.0,
                'productHeight': 5.0,
                'productWeight': (getattr(item.product, 'weight', None) or 0.5) * 1000,  # Convert to grams
                'brand': getattr(item.product.brand, 'name', '') if (item.product and item.product.brand) else '',
                'imageURL': '',  # Product image URL if available
                'isFragile': False,
                'isPersonalisable': False
            })

        # Format package details
        package_details = {
            'packageLength': package_dimensions['packageLength'],
            'packageBreadth': package_dimensions['packageBreadth'],
            'packageHeight': package_dimensions['packageHeight'],
            'packageWeight': package_weight * 1000  # Convert to grams
        }

        # Prepare complete order data for wrapper API
        return {
            'orderId': str(order.id),
            'orderDate': order.created_at.strftime('%Y-%m-%d'),
            'pickupAddressName': config.pickup_address_name,
            'pickupLocation': pickup_location,
            'storeName': config.store_name,
            'billingIsShipping': True,  # Assuming billing and shipping are same
            'shippingAddress': shipping_address,
            'orderItems': order_items,
            'paymentMethod': 'COD' if order.payments.filter(payment_method='COD').exists() else 'PREPAID',
            'shippingCharges': float(order.shipping_cost),
            'totalOrderValue': float(order.total),
            'packageDetails': package_details,
            'courierCode': int(courier_code)  # Add the courier code as integer to the order data
        }

    def _create_rapidshyp_shipment(self, order: Order, rapidshyp_response: Dict,
                                 courier_code: str) -> RapidshypShipment:
        """
        Create RapidshypShipment record from API response

        Args:
            order: Django Order instance
            rapidshyp_response: Rapidshyp API response
            courier_code: Selected courier code

        Returns:
            RapidshypShipment: Created shipment instance
        """
        with transaction.atomic():
            # Extract data from response
            order_data = rapidshyp_response.get('order_data', {})
            courier_data = rapidshyp_response.get('courier_data', {})

            shipment = RapidshypShipment.objects.create(
                order=order,
                rapidshyp_order_id=rapidshyp_response.get('order_id', ''),
                shipment_id=rapidshyp_response.get('shipment_id', ''),
                awb_number=rapidshyp_response.get('awb_number', ''),
                courier_code=courier_code,
                courier_name=courier_data.get('courier_name', ''),
                parent_courier_name=courier_data.get('parent_courier_name', ''),
                current_status='SCB',  # Shipment Booked
                total_freight=Decimal(str(courier_data.get('total_freight', 0))),
                freight_mode=courier_data.get('freight_mode', ''),
                cutoff_time=courier_data.get('cutoff_time', ''),
                max_weight=Decimal(str(courier_data.get('max_weight', 0))) if courier_data.get('max_weight') else None,
                min_weight=Decimal(str(courier_data.get('min_weight', 0))) if courier_data.get('min_weight') else None,
                rapidshyp_response_data=rapidshyp_response
            )

            # Update order with Rapidshyp data
            order.rapidshyp_enabled = True
            order.rapidshyp_order_id = shipment.rapidshyp_order_id
            order.selected_courier_code = courier_code
            order.save()

            # Create initial tracking event
            TrackingEvent.objects.create(
                shipment=shipment,
                status='SCB',
                status_description='Shipment Booked',
                location='',
                event_timestamp=timezone.now()
            )

            return shipment

    def _update_order_status_from_shipment(self, shipment: RapidshypShipment) -> None:
        """
        Update order status based on shipment status

        Args:
            shipment: RapidshypShipment instance
        """
        try:
            order = shipment.order
            rapidshyp_status = shipment.current_status

            # Map Rapidshyp status to order status
            new_order_status = RAPIDSHYP_TO_ORDER_STATUS.get(rapidshyp_status)

            if new_order_status and new_order_status != order.status:
                old_status = order.status
                order.status = new_order_status

                # Update delivery date if delivered
                if rapidshyp_status == 'DEL' and not shipment.actual_delivery_date:
                    shipment.actual_delivery_date = timezone.now()
                    shipment.save()

                order.save()

                self.logger.info(f"Order {order.id} status updated: {old_status} -> {new_order_status}")

        except Exception as e:
            self.logger.error(f"Failed to update order status: {e}")

    def is_rapidshyp_available(self) -> bool:
        """
        Check if Rapidshyp service is available

        Returns:
            bool: True if Rapidshyp is available
        """
        if not self.rapidshyp_enabled:
            return False

        return self.rapidshyp_client.is_api_available()

    def get_service_status(self) -> Dict:
        """
        Get comprehensive service status

        Returns:
            Dict: Service status information
        """
        rapidshyp_available = self.is_rapidshyp_available()

        return {
            'rapidshyp_enabled': self.rapidshyp_enabled,
            'rapidshyp_available': rapidshyp_available,
            'rapidshyp_api_key_configured': bool(self.rapidshyp_client.api_key),
            'fallback_available': True,  # Fallback is always available
            'existing_methods_count': len(self.fallback_service.get_existing_methods()),
            'service_mode': 'rapidshyp' if rapidshyp_available else 'fallback'
        }
